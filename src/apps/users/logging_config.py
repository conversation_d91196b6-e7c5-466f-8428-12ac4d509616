"""
Logging configuration for users app
"""
import logging
import os
from django.conf import settings


def setup_user_logging():
    """
    Setup logging configuration for users app
    """
    # Create logs directory if it doesn't exist
    log_dir = os.path.join(settings.BASE_DIR, 'logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configure logger for users app
    logger = logging.getLogger('users')
    logger.setLevel(logging.INFO)
    
    # Create file handler
    file_handler = logging.FileHandler(
        os.path.join(log_dir, 'users.log'),
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    
    # Add formatter to handlers
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add handlers to logger
    if not logger.handlers:
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
    
    return logger


# Setup logging when module is imported
setup_user_logging()
