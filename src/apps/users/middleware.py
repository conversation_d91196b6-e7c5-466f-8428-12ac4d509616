"""
Custom middleware for users app
"""
import logging
import time
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger('users')


class UserRequestLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log user-related requests
    """
    
    def process_request(self, request):
        """
        Log incoming requests to user endpoints
        """
        if request.path.startswith('/api/users/'):
            request.start_time = time.time()
            logger.info(
                f"Request started: {request.method} {request.path} "
                f"from {request.META.get('REMOTE_ADDR', 'unknown')}"
            )
    
    def process_response(self, request, response):
        """
        Log response details for user endpoints
        """
        if hasattr(request, 'start_time') and request.path.startswith('/api/users/'):
            duration = time.time() - request.start_time
            logger.info(
                f"Request completed: {request.method} {request.path} "
                f"- Status: {response.status_code} - Duration: {duration:.2f}s"
            )
        
        return response
    
    def process_exception(self, request, exception):
        """
        Log exceptions in user endpoints
        """
        if request.path.startswith('/api/users/'):
            logger.error(
                f"Exception in {request.method} {request.path}: "
                f"{type(exception).__name__}: {str(exception)}"
            )
        
        return None
