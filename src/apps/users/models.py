from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone
from django.core.validators import RegexValidator
import re


class User(AbstractUser):
    phone_regex = RegexValidator(
        regex=r'^\+?1?\d{9,15}$',
        message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed."
    )
    image_url = models.CharField(max_length=200, null=True, blank=True)
    phone_number = models.CharField(
        validators=[phone_regex],
        max_length=17,
        null=False,
        blank=False,
        help_text="Phone number in international format"
    )
    date_deleted = models.DateTimeField(null=True, blank=True)
    date_updated = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'users_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.username} ({self.email})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip()

    @property
    def is_deleted(self):
        return self.date_deleted is not None
    
    @classmethod
    def create_user_with_profile(cls, first_name, last_name, username, email, password, phone_number=None, image_url=None):
        # Validate required fields
        if not all([first_name, last_name, username, email, password]):
            raise ValueError("All required fields must be provided")

        # Create user with Django's built-in method
        user = cls.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name=first_name,
            last_name=last_name
        )

        # Add additional profile information
        if phone_number:
            user.phone_number = phone_number
        if image_url:
            user.image_url = image_url

        user.save()
        return user
    

    def modify_user(self, **kwargs):
        """
        Modify user fields with validation

        Args:
            **kwargs: Fields to update

        Returns:
            User: Updated user instance
        """
        allowed_fields = [
            'first_name', 'last_name', 'username', 'email',
            'phone_number', 'image_url'
        ]

        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(self, field):
                setattr(self, field, value)
            elif field not in allowed_fields:
                raise ValueError(f"Field '{field}' is not allowed to be modified")

        self.date_updated = timezone.now()
        return self

    
    def soft_delete(self):
        """
        Soft delete the user by setting date_deleted

        Returns:
            User: Soft deleted user instance
        """
        if self.date_deleted is not None:
            raise ValueError("User is already deleted")

        self.date_deleted = timezone.now()
        self.is_active = False  # Deactivate the user
        self.save()
        return self

    def restore(self):
        """
        Restore a soft deleted user

        Returns:
            User: Restored user instance
        """
        if self.date_deleted is None:
            raise ValueError("User is not deleted")

        self.date_deleted = None
        self.is_active = True
        self.save()
        return self

    def hard_delete(self):
        """
        Permanently delete the user
        """
        super().delete()