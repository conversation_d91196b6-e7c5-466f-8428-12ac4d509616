from rest_framework import serializers
from .services.user_service import UserService
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema_field
from rest_framework_simplejwt.tokens import RefreshToken
User = get_user_model()

class UserRegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    image = serializers.ImageField(required=False, allow_null=True)
    
    class Meta:
        model = User
        fields = ["first_name","last_name","username", "email" ,"phone_number","password", "image"]
        
    def create(self, validated_data):
        firstname=validated_data.get("first_name")
        lastname=validated_data.get("last_name")
        username = validated_data.get("username")
        email = validated_data.get("email")
        phone_number=validated_data.get("phone_number")
        password = validated_data.get("password")
        image_file=validated_data.pop('image',None)
        user = UserService.create_user(
            first_name=firstname,
            last_name=lastname,
            username=username,
            email=email,
            phone_number=phone_number,
            password=password,
            image=image_file
        )

    def to_representation(self, instance):
        """اللي راح ينعرض بالـ response"""
        refresh = getattr(self, "refresh", None)
        data = {
            "first_name": instance.first_name,
            "last_name": instance.last_name,
            "username": instance.username,
            "email": instance.email,
            "image_url": getattr(instance, "image_url", None),
        }
        if refresh:
            data["access_token"] = str(refresh.access_token)
            data["refresh_token"] = str(refresh)
        return data
    def to_representation(self, instance):
        """اللي راح ينعرض بالـ response"""
        refresh = getattr(self, "refresh", None)
        data = {
            "first_name": instance.first_name,
            "last_name": instance.last_name,
            "username": instance.username,
            "email": instance.email,
            "image_url": getattr(instance, "image_url", None),
        }
        if refresh:
            data["access_token"] = str(refresh.access_token)
            data["refresh_token"] = str(refresh)
        return data
    
class UserModifySerializer(serializers.ModelSerializer):
    class Meta :
        model=User
        fields=["first_name","last_name","username","email","image_url"]
