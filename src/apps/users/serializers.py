import logging
import re
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError as DjangoValidationError
from drf_spectacular.utils import extend_schema_field
from rest_framework_simplejwt.tokens import RefreshToken

from .services.user_service import UserService
from .exceptions import (
    UserAlreadyExistsException,
    InvalidUserDataException,
    FileUploadException
)

User = get_user_model()
logger = logging.getLogger(__name__)

class UserRegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True,
        min_length=8,
        help_text="Password must be at least 8 characters long"
    )
    password_confirm = serializers.CharField(
        write_only=True,
        help_text="Confirm your password"
    )
    image = serializers.ImageField(
        required=False,
        allow_null=True,
        help_text="Profile image (optional)"
    )

    class Meta:
        model = User
        fields = [
            "first_name", "last_name", "username", "email",
            "phone_number", "password", "password_confirm", "image"
        ]
        extra_kwargs = {
            'first_name': {'required': True, 'allow_blank': False},
            'last_name': {'required': True, 'allow_blank': False},
            'username': {'required': True, 'allow_blank': False},
            'email': {'required': True, 'allow_blank': False},
            'phone_number': {'required': True, 'allow_blank': False},
        }

    def validate_username(self, value):
        """Validate username format and uniqueness"""
        if not re.match(r'^[a-zA-Z0-9_]+$', value):
            raise serializers.ValidationError(
                "Username can only contain letters, numbers, and underscores"
            )

        if len(value) < 3:
            raise serializers.ValidationError(
                "Username must be at least 3 characters long"
            )

        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError(
                "A user with this username already exists"
            )

        return value

    def validate_email(self, value):
        """Validate email format and uniqueness"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError(
                "A user with this email already exists"
            )
        return value

    def validate_phone_number(self, value):
        """Validate phone number format"""
        if not re.match(r'^\+?1?\d{9,15}$', value):
            raise serializers.ValidationError(
                "Phone number must be in format: '+999999999'. Up to 15 digits allowed."
            )
        return value

    def validate_password(self, value):
        """Validate password strength"""
        try:
            validate_password(value)
        except DjangoValidationError as e:
            raise serializers.ValidationError(list(e.messages))

        # Additional custom validation
        if not re.search(r'[A-Za-z]', value):
            raise serializers.ValidationError(
                "Password must contain at least one letter"
            )

        if not re.search(r'\d', value):
            raise serializers.ValidationError(
                "Password must contain at least one digit"
            )

        return value

    def validate(self, attrs):
        """Validate the entire serializer data"""
        password = attrs.get('password')
        password_confirm = attrs.get('password_confirm')

        if password != password_confirm:
            raise serializers.ValidationError({
                'password_confirm': 'Passwords do not match'
            })

        # Remove password_confirm from validated data
        attrs.pop('password_confirm', None)
        return attrs

    def create(self, validated_data):
        """
        Create a new user with proper error handling

        Args:
            validated_data (dict): Validated user data

        Returns:
            User: Created user instance

        Raises:
            serializers.ValidationError: If user creation fails
        """
        logger.info(f"Creating new user with username: {validated_data.get('username')}")

        try:
            # Extract data
            first_name = validated_data.get("first_name")
            last_name = validated_data.get("last_name")
            username = validated_data.get("username")
            email = validated_data.get("email")
            phone_number = validated_data.get("phone_number")
            password = validated_data.get("password")
            image_file = validated_data.pop('image', None)

            # Create user through service
            user = UserService.create_user(
                first_name=first_name,
                last_name=last_name,
                username=username,
                email=email,
                phone_number=phone_number,
                password=password,
                image=image_file
            )

            # Generate JWT tokens
            self.refresh = RefreshToken.for_user(user)

            logger.info(f"User created successfully: {username}")
            return user

        except (UserAlreadyExistsException, InvalidUserDataException, FileUploadException) as e:
            logger.error(f"User creation failed: {str(e)}")
            raise serializers.ValidationError(str(e))
        except Exception as e:
            logger.error(f"Unexpected error during user creation: {str(e)}")
            raise serializers.ValidationError("An unexpected error occurred during user creation")
    def to_representation(self, instance):
        """
        Customize the response data format

        Args:
            instance (User): User instance

        Returns:
            dict: Formatted response data
        """
        refresh = getattr(self, "refresh", None)

        data = {
            "user": {
                "id": instance.id,
                "first_name": instance.first_name,
                "last_name": instance.last_name,
                "full_name": instance.full_name,
                "username": instance.username,
                "email": instance.email,
                "phone_number": instance.phone_number,
                "image_url": getattr(instance, "image_url", None),
                "date_joined": instance.date_joined.isoformat() if instance.date_joined else None,
            },
            "success": True,
            "message": "User registered successfully"
        }

        # Add JWT tokens if available
        if refresh:
            data["tokens"] = {
                "access_token": str(refresh.access_token),
                "refresh_token": str(refresh),
                "token_type": "Bearer"
            }

        return data
        
    
class UserModifySerializer(serializers.ModelSerializer):
    class Meta :
        model=User
        fields=["first_name","last_name","username","email","image_url"]
