import logging
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction
from django.db.models import Q
from users.models import User
from users.exceptions import (
    UserAlreadyExistsException,
    UserNotFoundException,
    InvalidUserDataException,
    FileUploadException
)
from common.services.file_service import FileService

User = get_user_model()
logger = logging.getLogger(__name__)
class UserService:
    @staticmethod
    @transaction.atomic
    def create_user(first_name, last_name, username, email, phone_number, password, image=None):
        """
        Create a new user with proper validation and error handling

        Args:
            first_name (str): User's first name
            last_name (str): User's last name
            username (str): Unique username
            email (str): Unique email address
            phone_number (str): User's phone number
            password (str): User's password
            image (File, optional): Profile image file

        Returns:
            User: Created user instance

        Raises:
            UserAlreadyExistsException: If username or email already exists
            FileUploadException: If image upload fails
            InvalidUserDataException: If provided data is invalid
        """
        logger.info(f"Attempting to create user with username: {username}")

        # Validate input data
        if not all([first_name, last_name, username, email, phone_number, password]):
            raise InvalidUserDataException("All required fields must be provided")

        # Check for existing username
        if User.objects.filter(username=username).exists():
            logger.warning(f"Attempt to create user with existing username: {username}")
            raise UserAlreadyExistsException(f"User with username '{username}' already exists")

        # Check for existing email
        if User.objects.filter(email=email).exists():
            logger.warning(f"Attempt to create user with existing email: {email}")
            raise UserAlreadyExistsException(f"User with email '{email}' already exists")

        # Handle image upload
        image_url = None
        if image is not None:
            try:
                image_url = FileService.upload(image, 'storage/public')
                logger.info(f"Image uploaded successfully for user: {username}")
            except Exception as e:
                logger.error(f"Image upload failed for user {username}: {str(e)}")
                raise FileUploadException(f"Failed to upload image: {str(e)}")

        try:
            # Create user using the model method
            user = User.create_user_with_profile(
                first_name=first_name,
                last_name=last_name,
                username=username,
                email=email,
                phone_number=phone_number,
                password=password,
                image_url=image_url
            )

            logger.info(f"User created successfully: {username}")
            return user

        except Exception as e:
            logger.error(f"Failed to create user {username}: {str(e)}")
            raise InvalidUserDataException(f"Failed to create user: {str(e)}")
    @staticmethod
    @transaction.atomic
    def modify_user(user_id, first_name, last_name, username, email, image=None):
        """
        Modify an existing user with proper validation and error handling

        Args:
            user_id (int): ID of the user to modify
            first_name (str): User's first name
            last_name (str): User's last name
            username (str): Unique username
            email (str): Unique email address
            image (File, optional): New profile image file

        Returns:
            User: Modified user instance

        Raises:
            UserNotFoundException: If user with given ID doesn't exist
            UserAlreadyExistsException: If username or email already exists for another user
            FileUploadException: If image upload fails
            InvalidUserDataException: If provided data is invalid
        """
        logger.info(f"Attempting to modify user with ID: {user_id}")

        # Validate input data
        if not all([first_name, last_name, username, email]):
            raise InvalidUserDataException("All required fields must be provided")

        # Get the user
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.warning(f"Attempt to modify non-existent user with ID: {user_id}")
            raise UserNotFoundException(f"User with ID '{user_id}' not found")

        # Check for existing username (excluding current user)
        if User.objects.filter(~Q(id=user_id), username=username).exists():
            logger.warning(f"Attempt to modify user {user_id} with existing username: {username}")
            raise UserAlreadyExistsException(f"Username '{username}' already exists")

        # Check for existing email (excluding current user)
        if User.objects.filter(~Q(id=user_id), email=email).exists():
            logger.warning(f"Attempt to modify user {user_id} with existing email: {email}")
            raise UserAlreadyExistsException(f"Email '{email}' already exists")

        # Handle image upload
        image_url = None
        if image is not None:
            try:
                # Delete old image if exists
                if user.image_url:
                    FileService.delete(user.image_url)

                image_url = FileService.upload(image, "storage/public")
                logger.info(f"Image updated successfully for user: {user_id}")
            except Exception as e:
                logger.error(f"Image upload failed for user {user_id}: {str(e)}")
                raise FileUploadException(f"Failed to upload image: {str(e)}")

        try:
            # Update user data
            user.modify_user(
                first_name=first_name,
                last_name=last_name,
                username=username,
                email=email,
                image_url=image_url
            )
            user.save()

            logger.info(f"User modified successfully: {user_id}")
            return user

        except Exception as e:
            logger.error(f"Failed to modify user {user_id}: {str(e)}")
            raise InvalidUserDataException(f"Failed to modify user: {str(e)}")
    