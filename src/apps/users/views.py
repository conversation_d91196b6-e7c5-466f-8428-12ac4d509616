from rest_framework import generics
from .serializers import UserRegisterSerializer
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiTypes, OpenApiParameter
from rest_framework.parsers import MultiPartParser, FormParser
User = get_user_model()

class RegisterView(generics.CreateAPIView):
    parser_classes = (FormParser,MultiPartParser)
    queryset = User.objects.all()
    serializer_class = UserRegisterSerializer

