import logging
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONPars<PERSON>
from rest_framework.permissions import AllowAny
from django.contrib.auth import get_user_model
from drf_spectacular.utils import extend_schema, OpenApiResponse, OpenApiExample

from .serializers import UserRegisterSerializer
from .exceptions import custom_exception_handler

User = get_user_model()
logger = logging.getLogger(__name__)


class RegisterView(generics.CreateAPIView):
    """
    User Registration API View

    Allows new users to register with the system.
    Supports both JSON and multipart form data for file uploads.
    """
    parser_classes = (<PERSON><PERSON><PERSON>ars<PERSON>, FormParser, MultiPartParser)
    queryset = User.objects.all()
    serializer_class = UserRegisterSerializer
    permission_classes = [AllowAny]

    @extend_schema(
        summary="Register a new user",
        description="Create a new user account with profile information and optional image upload",
        responses={
            201: OpenApiResponse(
                description="User created successfully",
                examples=[
                    OpenApiExample(
                        "Success Response",
                        value={
                            "user": {
                                "id": 1,
                                "first_name": "<PERSON>",
                                "last_name": "<PERSON><PERSON>",
                                "full_name": "<PERSON> Doe",
                                "username": "johndoe",
                                "email": "<EMAIL>",
                                "phone_number": "+**********",
                                "image_url": "https://example.com/image.jpg",
                                "date_joined": "2023-01-01T00:00:00Z"
                            },
                            "tokens": {
                                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                                "token_type": "Bearer"
                            },
                            "success": True,
                            "message": "User registered successfully"
                        }
                    )
                ]
            ),
            400: OpenApiResponse(
                description="Bad Request - Validation errors",
                examples=[
                    OpenApiExample(
                        "Validation Error",
                        value={
                            "error": {
                                "code": "USER_VALIDATION_ERROR",
                                "message": "Validation failed",
                                "details": {
                                    "username": ["A user with this username already exists"],
                                    "password": ["Password must contain at least one digit"]
                                }
                            },
                            "success": False
                        }
                    )
                ]
            ),
            409: OpenApiResponse(
                description="Conflict - User already exists",
                examples=[
                    OpenApiExample(
                        "User Exists",
                        value={
                            "error": {
                                "code": "USER_ALREADY_EXISTS",
                                "message": "User with username 'johndoe' already exists",
                                "details": "User already exists"
                            },
                            "success": False
                        }
                    )
                ]
            )
        },
        tags=["Authentication"]
    )
    def post(self, request, *args, **kwargs):
        """
        Create a new user account

        Args:
            request: HTTP request containing user data

        Returns:
            Response: JSON response with user data and tokens or error details
        """
        logger.info(f"User registration attempt from IP: {request.META.get('REMOTE_ADDR')}")

        try:
            return super().create(request, *args, **kwargs)
        except Exception as e:
            logger.error(f"Unexpected error in user registration: {str(e)}")
            return Response(
                {
                    "error": {
                        "code": "INTERNAL_ERROR",
                        "message": "An unexpected error occurred",
                        "details": "Please try again later"
                    },
                    "success": False
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request, *args, **kwargs):
        """
        Override create method to customize response format
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user = serializer.save()
        headers = self.get_success_headers(serializer.data)

        logger.info(f"User registered successfully: {user.username}")

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )

